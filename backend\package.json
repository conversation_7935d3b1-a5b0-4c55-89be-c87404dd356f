{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon src/index.js"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "cors": "^2.8.5", "cron": "^4.3.2", "dotenv": "^17.2.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}